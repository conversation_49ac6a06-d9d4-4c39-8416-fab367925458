import './HangmanDisplay.css'

function HangmanDisplay({ wrongGuesses }) {
  const hangmanParts = [
    '', // 0 wrong guesses
    '  +---+\n  |   |\n  |   |\n  O   |\n  |   |\n      |\n=========', // 1 - head and body
    '  +---+\n  |   |\n  |   |\n  O   |\n /|\\  |\n      |\n=========', // 2 - arms
    '  +---+\n  |   |\n  |   |\n  O   |\n /|\\  |\n / \\  |\n=========' // 3 - legs (dead)
  ]

  return (
    <div className="hangman-display">
      <pre className="hangman-ascii">
        {hangmanParts[Math.min(wrongGuesses, hangmanParts.length - 1)]}
      </pre>
      <div className="wrong-guesses-info">
        <p>Wrong guesses: {wrongGuesses}/3</p>
        <div className="lives-remaining">
          Lives remaining: {Math.max(0, 3 - wrongGuesses)}
        </div>
      </div>
    </div>
  )
}

export default HangmanDisplay
