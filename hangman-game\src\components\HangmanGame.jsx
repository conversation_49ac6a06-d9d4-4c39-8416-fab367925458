import { useState, useEffect } from 'react'
import ProgressBar from './ProgressBar'
import HangmanDisplay from './HangmanDisplay'
import WordDisplay from './WordDisplay'
import LetterInput from './LetterInput'
import HelpButton from './HelpButton'
import './HangmanGame.css'

const GAME_DATA = [
  {
    id: 1,
    answer: 'ACCOUNTABILITY',
    hint: 'Responsibility for one\'s actions and decisions'
  },
  {
    id: 2,
    answer: 'DIGITAL READINESS',
    hint: 'Preparedness for technological advancement'
  },
  {
    id: 3,
    answer: 'RESULTS',
    hint: 'Outcomes or consequences of actions'
  },
  {
    id: 4,
    answer: 'INNOVATION',
    hint: 'Creative implementation of new ideas'
  },
  {
    id: 5,
    answer: 'INCLUSION',
    hint: 'Practice of involving all individuals'
  }
]

const MAX_WRONG_GUESSES = 6

function HangmanGame() {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [guessedLetters, setGuessedLetters] = useState([])
  const [wrongGuesses, setWrongGuesses] = useState(0)
  const [gameStatus, setGameStatus] = useState('playing') // 'playing', 'won', 'lost', 'failed'
  const [helpUsed, setHelpUsed] = useState(Array(GAME_DATA.length).fill(false))
  const [completedQuestions, setCompletedQuestions] = useState(Array(GAME_DATA.length).fill(false))
  const [showCelebration, setShowCelebration] = useState(false)
  const [showFailureOverlay, setShowFailureOverlay] = useState(false)
  const [extraHelpUsed, setExtraHelpUsed] = useState(Array(GAME_DATA.length).fill(false))
  const [showStartOverlay, setShowStartOverlay] = useState(true)

  const currentQuestion = GAME_DATA[currentQuestionIndex]
  const currentAnswer = currentQuestion.answer.toUpperCase()

  // Check if word is complete
  const isWordComplete = () => {
    return currentAnswer.split('').every(letter => 
      letter === ' ' || guessedLetters.includes(letter)
    )
  }

  // Check game status
  useEffect(() => {
    if (isWordComplete()) {
      setGameStatus('won')
      const newCompleted = [...completedQuestions]
      newCompleted[currentQuestionIndex] = true
      setCompletedQuestions(newCompleted)
      setShowCelebration(true)

      setTimeout(() => {
        setShowCelebration(false)
        if (currentQuestionIndex < GAME_DATA.length - 1) {
          // Move to next question after a delay
          setTimeout(() => {
            moveToNextQuestion()
          }, 1000)
        }
      }, 2000)
    } else if (wrongGuesses >= MAX_WRONG_GUESSES) {
      setGameStatus('failed')
      setShowFailureOverlay(true)
    }
  }, [guessedLetters, wrongGuesses, currentQuestionIndex])

  const handleLetterGuess = (letter) => {
    if (guessedLetters.includes(letter) || gameStatus !== 'playing') return

    const newGuessedLetters = [...guessedLetters, letter]
    setGuessedLetters(newGuessedLetters)

    if (!currentAnswer.includes(letter)) {
      setWrongGuesses(wrongGuesses + 1)
    }
  }

  const handleHelp = () => {
    const newHelpUsed = [...helpUsed]
    newHelpUsed[currentQuestionIndex] = true
    setHelpUsed(newHelpUsed)

    const newCompleted = [...completedQuestions]
    newCompleted[currentQuestionIndex] = true
    setCompletedQuestions(newCompleted)

    if (currentQuestionIndex < GAME_DATA.length - 1) {
      setTimeout(() => {
        moveToNextQuestion()
      }, 1000)
    } else {
      setGameStatus('won')
    }
  }

  const handleExtraHelp = () => {
    const newExtraHelpUsed = [...extraHelpUsed]
    newExtraHelpUsed[currentQuestionIndex] = true
    setExtraHelpUsed(newExtraHelpUsed)

    const newCompleted = [...completedQuestions]
    newCompleted[currentQuestionIndex] = true
    setCompletedQuestions(newCompleted)

    setShowFailureOverlay(false)

    if (currentQuestionIndex < GAME_DATA.length - 1) {
      setTimeout(() => {
        moveToNextQuestion()
      }, 1000)
    } else {
      setGameStatus('won')
    }
  }



  const moveToNextQuestion = () => {
    if (currentQuestionIndex < GAME_DATA.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
      setGuessedLetters([])
      setWrongGuesses(0)
      setGameStatus('playing')
    }
  }

  const handleStartGame = () => {
    setShowStartOverlay(false)
  }

  const resetGame = () => {
    setCurrentQuestionIndex(0)
    setGuessedLetters([])
    setWrongGuesses(0)
    setGameStatus('playing')
    setHelpUsed(Array(GAME_DATA.length).fill(false))
    setCompletedQuestions(Array(GAME_DATA.length).fill(false))
    setShowCelebration(false)
    setShowFailureOverlay(false)
    setExtraHelpUsed(Array(GAME_DATA.length).fill(false))
    setShowStartOverlay(true)
  }

  const isGameComplete = () => {
    return currentQuestionIndex === GAME_DATA.length - 1 && 
           (gameStatus === 'won' || completedQuestions[currentQuestionIndex])
  }

  return (
    <div className="hangman-game">
      {/* Start Overlay */}
      {showStartOverlay && (
        <div className="start-overlay">
          <div className="start-modal">
            <div className="task-header">
              <span className="task-badge">TASK</span>
              <h2 className="task-title">THE FORGOTTEN INCANTATION</h2>
              <span className="points-badge">⭐ 10,000 PTS</span>
            </div>

            <div className="puzzle-visual">
              <div className="puzzle-grid">
                <div className="puzzle-piece completed"></div>
                <div className="puzzle-piece completed"></div>
                <div className="puzzle-piece completed"></div>
                <div className="puzzle-piece completed"></div>
                <div className="puzzle-piece missing"></div>
                <div className="puzzle-piece completed"></div>
                <div className="puzzle-piece completed"></div>
                <div className="puzzle-piece completed"></div>
                <div className="puzzle-piece completed"></div>
              </div>
            </div>

            <div className="mission-brief">
              <h3>🎯 Mission Brief</h3>
              <p>Your team must guess the letters to reveal the HCCB Values and culture codes word before your connection to the magical lock is severed permanently.</p>

              <div className="how-to-play">
                <h4>How to Play:</h4>
                <ul>
                  <li><strong>Guess the Word:</strong> Uncover the secret incantation one letter at a time.</li>
                  <li><strong>3 Chances Only:</strong> You can only make 3 incorrect guesses. A fourth incorrect guess will sever the connection, and you will fail this task.</li>
                  <li><strong>Cost of Error:</strong> Every incorrect guess you make will immediately drain 300 Funds from your treasury.</li>
                  <li><strong>Seek Wisdom:</strong> If you are stuck, you can pay 150 Funds for a "Hint". This will reveal one correct letter in the word for you.</li>
                </ul>
              </div>

              <p className="warning">The potion's future depends on your wisdom. Choose your letters carefully.</p>
            </div>

            <div className="first-available">
              <h4>💎 First Available</h4>
              <p>Yes, this is a task that is a first option</p>
              <p>You can complete this task with a prize.</p>
              <div className="fund-changes">
                <span className="fund-amount">Fund Changes +1500 pts</span>
              </div>
            </div>

            <button onClick={handleStartGame} className="start-task-button">
              START TASK
            </button>
          </div>
        </div>
      )}

      {!showStartOverlay && (
        <>
          <ProgressBar
            total={GAME_DATA.length}
            completed={completedQuestions}
            current={currentQuestionIndex}
            helpUsed={helpUsed}
            extraHelpUsed={extraHelpUsed}
          />

      {showCelebration && (
        <div className="celebration">
          <h2>🎉 Correct! 🎉</h2>
        </div>
      )}

      {!isGameComplete() ? (
        <div className="game-content">
          <div className="question-info">
            <h2>Question {currentQuestionIndex + 1} of {GAME_DATA.length}</h2>
            <p className="hint">Hint: {currentQuestion.hint}</p>
          </div>

          <HangmanDisplay wrongGuesses={wrongGuesses} />
          
          <WordDisplay 
            word={currentAnswer}
            guessedLetters={guessedLetters}
          />

          <div className="game-controls">
            <LetterInput 
              onLetterGuess={handleLetterGuess}
              guessedLetters={guessedLetters}
              disabled={gameStatus !== 'playing'}
            />
            
            <HelpButton 
              onHelp={handleHelp}
              disabled={gameStatus !== 'playing' || helpUsed[currentQuestionIndex]}
              helpUsed={helpUsed[currentQuestionIndex]}
            />
          </div>


        </div>
      ) : (
        <div className="game-complete">
          <h2>🎊 Congratulations! 🎊</h2>
          <p>You've completed all hangman challenges!</p>
          <div className="final-stats">
            <p>Questions completed: {completedQuestions.filter(Boolean).length}/{GAME_DATA.length}</p>
            <p>Regular help used: {helpUsed.filter(Boolean).length} times</p>
            <p>Extra help used: {extraHelpUsed.filter(Boolean).length} times</p>
          </div>
          <button onClick={resetGame} className="reset-button">
            Play Again
          </button>
        </div>
      )}

      {/* Failure Overlay */}
      {showFailureOverlay && (
        <div className="failure-overlay">
          <div className="failure-modal">
            <h2>🎯 Challenge Failed!</h2>
            <p>You've used all your attempts for this word.</p>
            <p>Need help to proceed to the next question?</p>

            <div className="failure-actions">
              <button onClick={handleExtraHelp} className="extra-help-button">
                💰 Need Help? (Extra Charge)
              </button>
            </div>

            <div className="failure-info">
              <small>Click "Need Help" to skip this question with extra penalty and move to the next one</small>
            </div>
          </div>
        </div>
      )}
        </>
      )}
    </div>
  )
}

export default HangmanGame
