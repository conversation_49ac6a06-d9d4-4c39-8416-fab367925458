.hangman-game {
  max-width: 1000px;
  margin: 0 auto;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
  color: #ffffff;
  height: 100%;
  overflow-y: auto;
}



.celebration {
  text-align: center;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(75, 0, 130, 0.3));
  border: 1px solid rgba(138, 43, 226, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin: 12px 0;
  animation: bounce 0.6s ease-in-out;
  backdrop-filter: blur(10px);
}

.celebration h2 {
  font-size: 1.5rem;
  margin: 0;
  color: #a855f7;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.game-content {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.3);
}

.question-info {
  text-align: center;
  margin-bottom: 16px;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.1), rgba(75, 0, 130, 0.2));
  border: 1px solid rgba(138, 43, 226, 0.2);
  border-radius: 8px;
  padding: 12px;
}

.question-info h2 {
  color: #a855f7;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 1.3rem;
}

.hint {
  font-size: 0.95rem;
  font-style: italic;
  color: #cbd5e1;
  margin: 0;
}

.game-controls {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  margin-top: 16px;
  align-items: start;
}

.game-over {
  text-align: center;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(185, 28, 28, 0.3));
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
}

.game-over h2 {
  color: #ef4444;
  margin-bottom: 12px;
  font-weight: 600;
}

.game-complete {
  text-align: center;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(21, 128, 61, 0.3));
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 16px;
  padding: 40px;
  margin-top: 24px;
}

.game-complete h2 {
  color: #22c55e;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 24px;
}

.final-stats {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.6), rgba(15, 23, 42, 0.8));
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
}

.final-stats p {
  margin: 12px 0;
  font-size: 1.1rem;
  color: #cbd5e1;
}

.reset-button {
  background: linear-gradient(135deg, #a855f7, #7c3aed);
  color: white;
  border: 1px solid rgba(168, 85, 247, 0.3);
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(168, 85, 247, 0.3);
}

.reset-button:hover {
  background: linear-gradient(135deg, #9333ea, #6b21a8);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(168, 85, 247, 0.4);
}

.reset-button:active {
  transform: translateY(0);
}

@media (max-width: 768px) {
  .hangman-game {
    padding: 12px;
  }

  .hangman-game h1 {
    font-size: 1.5rem;
    margin-bottom: 12px;
  }

  .game-content {
    padding: 16px;
  }

  .game-controls {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* Failure Overlay Styles */
.failure-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.failure-modal {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(15, 23, 42, 0.98));
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0,0,0,0.5);
}

.failure-modal h2 {
  color: #ef4444;
  font-size: 1.5rem;
  margin-bottom: 16px;
  font-weight: 600;
}

.failure-modal p {
  color: #cbd5e1;
  margin-bottom: 12px;
  font-size: 1rem;
}

.failure-actions {
  display: flex;
  justify-content: center;
  margin: 24px 0 16px;
}

.extra-help-button {
  padding: 14px 32px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.extra-help-button:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.failure-info {
  color: rgba(203, 213, 225, 0.8);
  font-style: italic;
}

.failure-info small {
  font-size: 0.8rem;
}

/* Start Overlay Styles */
.start-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.start-modal {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.98), rgba(15, 23, 42, 0.99));
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 16px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0,0,0,0.7);
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.task-badge {
  background: #22c55e;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.task-title {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  text-align: center;
}

.points-badge {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.puzzle-visual {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.puzzle-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 120px;
  height: 120px;
}

.puzzle-piece {
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.puzzle-piece.completed {
  background: #22c55e;
  border-color: #16a34a;
}

.puzzle-piece.missing {
  background: #22c55e;
  border-color: #16a34a;
  position: relative;
}

.puzzle-piece.missing::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  background: #f8f9fa;
  border-radius: 4px;
}

.mission-brief {
  margin: 20px 0;
}

.mission-brief h3 {
  color: #22c55e;
  font-size: 1.1rem;
  margin-bottom: 12px;
  font-weight: 600;
}

.mission-brief p {
  color: #cbd5e1;
  line-height: 1.5;
  margin-bottom: 16px;
}

.how-to-play h4 {
  color: #a855f7;
  font-size: 1rem;
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.how-to-play ul {
  color: #cbd5e1;
  padding-left: 20px;
  line-height: 1.5;
}

.how-to-play li {
  margin-bottom: 8px;
}

.how-to-play strong {
  color: #ffffff;
}

.warning {
  color: #f59e0b !important;
  font-style: italic;
  font-weight: 500;
}

.first-available {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.2));
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin: 20px 0;
}

.first-available h4 {
  color: #22c55e;
  font-size: 1rem;
  margin-bottom: 8px;
  font-weight: 600;
}

.first-available p {
  color: #cbd5e1;
  margin: 4px 0;
  font-size: 0.9rem;
}

.fund-changes {
  margin-top: 12px;
}

.fund-amount {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

.start-task-button {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
  margin-top: 20px;
}

.start-task-button:hover {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
}
